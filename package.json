{"name": "psychiatric-assessment", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "prisma db push", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset", "db:seed": "tsx prisma/seed.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@prisma/client": "^5.8.1", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-query": "^5.17.19", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^0.2.0", "date-fns": "^3.2.0", "lucide-react": "^0.312.0", "next": "^14.2.31", "prisma": "^5.8.1", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.49.3", "react-window": "^1.8.8", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-window": "^1.8.8", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "postcss": "^8", "tailwindcss": "^3.3.0", "tsx": "^4.7.0", "typescript": "^5"}}