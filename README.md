# Psychiatric Assessment System

A comprehensive, production-ready web application for collecting psychiatric assessment data optimized for machine learning training. Built with modern technologies and designed for fast, reliable data collection.

## 🚀 Features

### Core Assessment Sections
- **Demographics**: Complete patient information and background
- **Symptoms**: Comprehensive symptom assessment with severity, duration, and frequency
- **Risk Assessment**: Safety evaluation including suicide, violence, and self-harm risk
- **Medical History**: Medical and psychiatric history collection
- **Mental Status Exam**: Current mental state evaluation
- **Diagnosis**: Diagnostic formulation with ICD-10 coding

### Performance & Reliability
- ⚡ **2-second debounced autosave** - Never lose data
- 💾 **Local storage backup** - Automatic fallback protection
- 🔄 **Real-time progress tracking** - Visual completion indicators
- 🎯 **Virtualized components** - Optimized for large datasets
- 📱 **Responsive design** - Works on all devices

### Data Export & ML Ready
- 📊 **CSV export** - Flattened structure for ML training
- 📄 **JSON export** - Complete hierarchical data
- 🏷️ **Structured metadata** - Timestamps and completion tracking
- 🔍 **Data management interface** - View and export all assessments

## 🛠️ Technology Stack

- **Frontend**: Next.js 14+ with App Router, React, TypeScript
- **Styling**: Tailwind CSS + shadcn/ui components
- **Database**: SQLite with Prisma ORM
- **Forms**: React Hook Form with validation
- **State Management**: Built-in React state + localStorage backup
- **Icons**: Lucide React

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd psychiatric-assessment
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up the database**
   ```bash
   npx prisma generate
   npx prisma db push
   npm run db:seed
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open in browser**
   ```
   http://localhost:3000
   ```

## 🎯 Usage

### Starting an Assessment
1. Navigate to the home page
2. Click "Start Assessment"
3. Complete the 6 assessment sections:
   - Demographics
   - Symptoms
   - Risk Assessment
   - Medical History
   - Mental Status Exam
   - Diagnosis

### Data Collection Features
- **Auto-save**: Data is automatically saved every 2 seconds
- **Progress tracking**: Visual indicators show completion status
- **Form validation**: Ensures data quality and completeness
- **Predefined options**: Quick selection for common fields

### Exporting Data
1. Navigate to "View Data & Export"
2. Choose export format:
   - **CSV**: For ML training and statistical analysis
   - **JSON**: For application integration and detailed analysis

## 📊 Data Structure

### CSV Export Features
- Flattened structure for easy ML processing
- Categorical variables properly encoded
- Boolean risk factors as binary features
- Timestamp data for temporal analysis
- Symptom and diagnosis counts as features

### JSON Export Features
- Complete hierarchical data structure
- Preserves all relationships and metadata
- Detailed symptom and diagnosis information
- Full text responses for NLP analysis
- Export metadata and timestamps

## 🗄️ Database Schema

The application uses a comprehensive database schema with the following main entities:

- **Assessment**: Main assessment record
- **Demographics**: Patient demographic information
- **SymptomAssessment**: Symptom evaluations with severity/frequency
- **RiskAssessment**: Safety and risk evaluations
- **MedicalHistory**: Medical and psychiatric history
- **MentalStatusExam**: Mental status examination results
- **DiagnosisAssessment**: Diagnostic formulations
- **Symptom**: Master list of symptoms
- **Diagnosis**: Master list of diagnoses with ICD-10 codes

## 🔧 Configuration

### Environment Variables
Create a `.env` file in the root directory:

```env
DATABASE_URL="file:./dev.db"
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

### Database Commands
```bash
# Generate Prisma client
npm run db:generate

# Push schema changes
npm run db:push

# Reset database
npm run db:reset

# Seed with sample data
npm run db:seed
```

## 🚀 Deployment

### Production Build
```bash
npm run build
npm start
```

### Environment Setup
1. Set up production database
2. Update `DATABASE_URL` in environment variables
3. Run database migrations
4. Deploy to your preferred platform

## 📈 ML Training Data

The exported CSV data includes:

### Demographic Features
- Age, gender, ethnicity, race
- Education, occupation, employment status
- Living arrangement, marital status, insurance

### Clinical Features
- Risk assessment scores (suicide, violence, self-harm)
- Symptom presence and severity
- Mental status exam findings
- Medical and psychiatric history indicators

### Outcome Variables
- Primary and secondary diagnoses
- ICD-10 diagnostic codes
- Treatment recommendations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the documentation
2. Search existing issues
3. Create a new issue with detailed information

## 🔮 Future Enhancements

- [ ] User authentication and role management
- [ ] Advanced analytics dashboard
- [ ] Integration with EHR systems
- [ ] Multi-language support
- [ ] Advanced ML model integration
- [ ] Automated risk scoring
- [ ] Report generation

---

Built with ❤️ for mental health professionals and researchers.
