import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const format = searchParams.get('format') || 'json'

    // Get all assessments with complete data
    const assessments = await db.assessment.findMany({
      include: {
        demographics: true,
        riskAssessment: true,
        medicalHistory: true,
        mentalStatusExam: true,
        symptoms: {
          include: {
            symptom: true
          }
        },
        diagnoses: {
          include: {
            diagnosis: true
          }
        }
      }
    })

    if (format === 'csv') {
      // Convert to flat structure for CSV
      const flatData = assessments.map(assessment => {
        const symptoms = assessment.symptoms.map(s => s.symptom.name).join(';')
        const diagnoses = assessment.diagnoses.map(d => d.diagnosis.name).join(';')
        
        return {
          // Assessment metadata
          assessment_id: assessment.id,
          assessment_date: assessment.assessmentDate.toISOString(),
          assessor_name: assessment.assessorName,
          status: assessment.status,
          
          // Demographics
          age: assessment.demographics?.age || null,
          gender: assessment.demographics?.gender || null,
          ethnicity: assessment.demographics?.ethnicity || null,
          race: assessment.demographics?.race || null,
          education: assessment.demographics?.education || null,
          occupation: assessment.demographics?.occupation || null,
          employment_status: assessment.demographics?.employmentStatus || null,
          living_arrangement: assessment.demographics?.livingArrangement || null,
          marital_status: assessment.demographics?.maritalStatus || null,
          insurance_type: assessment.demographics?.insuranceType || null,
          
          // Risk Assessment
          suicidal_ideation: assessment.riskAssessment?.suicidalIdeation || false,
          suicidal_plan: assessment.riskAssessment?.suicidalPlan || false,
          suicidal_means: assessment.riskAssessment?.suicidalMeans || false,
          suicidal_attempt_history: assessment.riskAssessment?.suicidalAttemptHistory || false,
          suicidal_risk_level: assessment.riskAssessment?.suicidalRiskLevel || null,
          homicidal_ideation: assessment.riskAssessment?.homicidalIdeation || false,
          violence_history: assessment.riskAssessment?.violenceHistory || false,
          violence_risk_level: assessment.riskAssessment?.violenceRiskLevel || null,
          self_harm_history: assessment.riskAssessment?.selfHarmHistory || false,
          self_harm_risk: assessment.riskAssessment?.selfHarmRisk || null,
          substance_use_risk: assessment.riskAssessment?.substanceUseRisk || null,
          
          // Medical History
          previous_psychiatric_treatment: assessment.medicalHistory?.previousPsychiatricTreatment || false,
          trauma_history: assessment.medicalHistory?.traumaHistory || false,
          
          // Mental Status Exam
          appearance: assessment.mentalStatusExam?.appearance || null,
          behavior: assessment.mentalStatusExam?.behavior || null,
          mood: assessment.mentalStatusExam?.mood || null,
          affect: assessment.mentalStatusExam?.affect || null,
          thought_process: assessment.mentalStatusExam?.thoughtProcess || null,
          hallucinations: assessment.mentalStatusExam?.hallucinations || false,
          delusions: assessment.mentalStatusExam?.delusions || false,
          orientation: assessment.mentalStatusExam?.orientation || null,
          insight: assessment.mentalStatusExam?.insight || null,
          judgment: assessment.mentalStatusExam?.judgment || null,
          
          // Symptoms and Diagnoses
          symptoms: symptoms,
          diagnoses: diagnoses,
          symptom_count: assessment.symptoms.length,
          diagnosis_count: assessment.diagnoses.length,
          
          // Timestamps
          created_at: assessment.createdAt.toISOString(),
          updated_at: assessment.updatedAt.toISOString()
        }
      })

      // Convert to CSV
      if (flatData.length === 0) {
        return new NextResponse('No data available', { status: 404 })
      }

      const headers = Object.keys(flatData[0])
      const csvContent = [
        headers.join(','),
        ...flatData.map(row => 
          headers.map(header => {
            const value = row[header as keyof typeof row]
            // Escape commas and quotes in CSV
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
              return `"${value.replace(/"/g, '""')}"`
            }
            return value || ''
          }).join(',')
        )
      ].join('\n')

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="psychiatric-assessments-${new Date().toISOString().split('T')[0]}.csv"`
        }
      })

    } else {
      // Return JSON format
      const exportData = {
        metadata: {
          export_date: new Date().toISOString(),
          total_assessments: assessments.length,
          format: 'json'
        },
        assessments: assessments
      }

      return NextResponse.json(exportData, {
        headers: {
          'Content-Disposition': `attachment; filename="psychiatric-assessments-${new Date().toISOString().split('T')[0]}.json"`
        }
      })
    }

  } catch (error) {
    console.error('Error exporting data:', error)
    return NextResponse.json(
      { error: 'Failed to export data' },
      { status: 500 }
    )
  }
}
