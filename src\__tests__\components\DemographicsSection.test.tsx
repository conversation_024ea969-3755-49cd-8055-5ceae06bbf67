import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import DemographicsSection from '@/components/assessment/DemographicsSection'

// Mock the constants module
jest.mock('@/lib/constants', () => ({
  EDUCATION_OPTIONS: ['High School', 'Bachelor\'s Degree', 'Master\'s Degree'],
  OCCUPATION_OPTIONS: ['Teacher', 'Engineer', 'Doctor'],
  LIVING_ARRANGEMENT_OPTIONS: ['Alone', 'With Family', 'With Roommates'],
  MARITAL_STATUS_OPTIONS: ['Single', 'Married', 'Divorced'],
  GENDER_OPTIONS: ['Male', 'Female', 'Other'],
  ETHNICITY_OPTIONS: ['Hispanic', 'Non-Hispanic'],
  RACE_OPTIONS: ['White', 'Black', 'Asian'],
  INSURANCE_OPTIONS: ['Private', 'Medicare', 'Medicaid'],
  EMPLOYMENT_STATUS_OPTIONS: ['Employed', 'Unemployed', 'Retired']
}))

describe('DemographicsSection', () => {
  const mockOnUpdate = jest.fn()
  const defaultData = {
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    age: undefined,
    gender: '',
    phone: '',
    email: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    ethnicity: '',
    race: '',
    primaryLanguage: '',
    maritalStatus: '',
    education: '',
    occupation: '',
    employmentStatus: '',
    livingArrangement: '',
    insuranceType: '',
    emergencyContact: '',
    emergencyPhone: ''
  }

  beforeEach(() => {
    mockOnUpdate.mockClear()
  })

  it('renders the demographics form', () => {
    render(<DemographicsSection data={defaultData} onUpdate={mockOnUpdate} />)
    
    expect(screen.getByText('Demographics')).toBeInTheDocument()
    expect(screen.getByLabelText(/first name/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/last name/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/date of birth/i)).toBeInTheDocument()
  })

  it('calls onUpdate when form fields are changed', async () => {
    const user = userEvent.setup()
    render(<DemographicsSection data={defaultData} onUpdate={mockOnUpdate} />)
    
    const firstNameInput = screen.getByLabelText(/first name/i)
    await user.type(firstNameInput, 'John')
    
    await waitFor(() => {
      expect(mockOnUpdate).toHaveBeenCalledWith(
        expect.objectContaining({ firstName: 'John' })
      )
    })
  })

  it('displays existing data in form fields', () => {
    const existingData = {
      ...defaultData,
      firstName: 'Jane',
      lastName: 'Doe',
      age: 30
    }
    
    render(<DemographicsSection data={existingData} onUpdate={mockOnUpdate} />)
    
    expect(screen.getByDisplayValue('Jane')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Doe')).toBeInTheDocument()
    expect(screen.getByDisplayValue('30')).toBeInTheDocument()
  })

  it('calculates age when date of birth is entered', async () => {
    const user = userEvent.setup()
    render(<DemographicsSection data={defaultData} onUpdate={mockOnUpdate} />)
    
    const dobInput = screen.getByLabelText(/date of birth/i)
    await user.type(dobInput, '1990-01-01')
    
    await waitFor(() => {
      expect(mockOnUpdate).toHaveBeenCalledWith(
        expect.objectContaining({ 
          dateOfBirth: '1990-01-01',
          age: expect.any(Number)
        })
      )
    })
  })
})
