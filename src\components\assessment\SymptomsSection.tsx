"use client"

import { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { SEVERITY_OPTIONS, DURATION_OPTIONS, FREQUENCY_OPTIONS } from "@/lib/constants"

interface SymptomsData {
  selectedSymptoms?: string[]
  symptomDetails?: Record<string, {
    severity?: string
    duration?: string
    frequency?: string
    notes?: string
  }>
  additionalSymptoms?: string
}

interface SymptomsSectionProps {
  data: SymptomsData
  onUpdate: (data: SymptomsData) => void
}

// Common psychiatric symptoms for quick selection
const COMMON_SYMPTOMS = [
  "Depressed mood",
  "Anxiety",
  "Sleep disturbances",
  "Appetite changes",
  "Fatigue",
  "Concentration difficulties",
  "Irritability",
  "Mood swings",
  "Panic attacks",
  "Obsessive thoughts",
  "Compulsive behaviors",
  "Hallucinations",
  "Delusions",
  "Paranoia",
  "Social withdrawal",
  "Suicidal thoughts",
  "Self-harm behaviors",
  "Substance use",
  "Memory problems",
  "Disorientation"
]

export default function SymptomsSection({ data, onUpdate }: SymptomsSectionProps) {
  const [formData, setFormData] = useState<SymptomsData>(data || {
    selectedSymptoms: [],
    symptomDetails: {},
    additionalSymptoms: ''
  })

  useEffect(() => {
    onUpdate(formData)
  }, [formData, onUpdate])

  const handleSymptomToggle = (symptom: string, checked: boolean) => {
    const newSelectedSymptoms = checked
      ? [...(formData.selectedSymptoms || []), symptom]
      : (formData.selectedSymptoms || []).filter(s => s !== symptom)

    setFormData(prev => ({
      ...prev,
      selectedSymptoms: newSelectedSymptoms
    }))
  }

  const handleSymptomDetailChange = (symptom: string, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      symptomDetails: {
        ...prev.symptomDetails,
        [symptom]: {
          ...prev.symptomDetails?.[symptom],
          [field]: value
        }
      }
    }))
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-2">Symptom Assessment</h2>
        <p className="text-sm text-slate-600">Select all symptoms that apply and provide additional details.</p>
      </div>

      {/* Common Symptoms Checklist */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Common Symptoms</CardTitle>
          <CardDescription>Check all symptoms that the patient is currently experiencing</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {COMMON_SYMPTOMS.map((symptom) => {
              const isSelected = formData.selectedSymptoms?.includes(symptom) || false
              return (
                <div key={symptom} className="flex items-center space-x-2">
                  <Checkbox
                    id={symptom}
                    checked={isSelected}
                    onCheckedChange={(checked) => handleSymptomToggle(symptom, checked as boolean)}
                  />
                  <Label htmlFor={symptom} className="text-sm font-normal cursor-pointer">
                    {symptom}
                  </Label>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Symptom Details */}
      {formData.selectedSymptoms && formData.selectedSymptoms.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Symptom Details</CardTitle>
            <CardDescription>Provide additional details for selected symptoms</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {formData.selectedSymptoms.map((symptom) => (
              <div key={symptom} className="border rounded-lg p-4 space-y-4">
                <h4 className="font-medium text-slate-900">{symptom}</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>Severity</Label>
                    <Select
                      value={formData.symptomDetails?.[symptom]?.severity || ''}
                      onValueChange={(value) => handleSymptomDetailChange(symptom, 'severity', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select severity" />
                      </SelectTrigger>
                      <SelectContent>
                        {SEVERITY_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Duration</Label>
                    <Select
                      value={formData.symptomDetails?.[symptom]?.duration || ''}
                      onValueChange={(value) => handleSymptomDetailChange(symptom, 'duration', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select duration" />
                      </SelectTrigger>
                      <SelectContent>
                        {DURATION_OPTIONS.map((option) => (
                          <SelectItem key={option} value={option}>
                            {option}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Frequency</Label>
                    <Select
                      value={formData.symptomDetails?.[symptom]?.frequency || ''}
                      onValueChange={(value) => handleSymptomDetailChange(symptom, 'frequency', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select frequency" />
                      </SelectTrigger>
                      <SelectContent>
                        {FREQUENCY_OPTIONS.map((option) => (
                          <SelectItem key={option} value={option}>
                            {option}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Additional Notes</Label>
                  <Textarea
                    value={formData.symptomDetails?.[symptom]?.notes || ''}
                    onChange={(e) => handleSymptomDetailChange(symptom, 'notes', e.target.value)}
                    placeholder="Any additional details about this symptom..."
                    rows={2}
                  />
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Additional Symptoms */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Additional Symptoms</CardTitle>
          <CardDescription>Describe any other symptoms not listed above</CardDescription>
        </CardHeader>
        <CardContent>
          <Textarea
            value={formData.additionalSymptoms || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, additionalSymptoms: e.target.value }))}
            placeholder="Describe any additional symptoms, their severity, duration, and impact..."
            rows={4}
          />
        </CardContent>
      </Card>
    </div>
  )
}
