"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/assessment/page",{

/***/ "(app-pages-browser)/./src/components/assessment/MentalStatusExamSection.tsx":
/*!***************************************************************!*\
  !*** ./src/components/assessment/MentalStatusExamSection.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MentalStatusExamSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst APPEARANCE_OPTIONS = [\n    \"Well-groomed\",\n    \"Disheveled\",\n    \"Appropriate dress\",\n    \"Inappropriate dress\",\n    \"Poor hygiene\"\n];\nconst BEHAVIOR_OPTIONS = [\n    \"Calm\",\n    \"Agitated\",\n    \"Restless\",\n    \"Withdrawn\",\n    \"Cooperative\",\n    \"Uncooperative\"\n];\nconst SPEECH_RATE_OPTIONS = [\n    \"Normal\",\n    \"Rapid\",\n    \"Slow\",\n    \"Pressured\"\n];\nconst SPEECH_VOLUME_OPTIONS = [\n    \"Normal\",\n    \"Loud\",\n    \"Soft\",\n    \"Whispered\"\n];\nconst MOOD_OPTIONS = [\n    \"Euthymic\",\n    \"Depressed\",\n    \"Anxious\",\n    \"Irritable\",\n    \"Euphoric\",\n    \"Angry\"\n];\nconst AFFECT_OPTIONS = [\n    \"Appropriate\",\n    \"Flat\",\n    \"Blunted\",\n    \"Labile\",\n    \"Inappropriate\"\n];\nconst THOUGHT_PROCESS_OPTIONS = [\n    \"Linear\",\n    \"Tangential\",\n    \"Circumstantial\",\n    \"Flight of ideas\",\n    \"Loose associations\"\n];\nconst ORIENTATION_OPTIONS = [\n    \"Oriented x3\",\n    \"Oriented x2\",\n    \"Oriented x1\",\n    \"Disoriented\"\n];\nconst INSIGHT_OPTIONS = [\n    \"Good\",\n    \"Fair\",\n    \"Poor\",\n    \"Absent\"\n];\nconst JUDGMENT_OPTIONS = [\n    \"Good\",\n    \"Fair\",\n    \"Poor\",\n    \"Impaired\"\n];\nfunction MentalStatusExamSection(param) {\n    let { data, onUpdate } = param;\n    var _formData_hallucinations, _formData_delusions;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(data || {});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        onUpdate(formData);\n    }, [\n        formData,\n        onUpdate\n    ]);\n    const handleBooleanChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value === \"true\"\n            }));\n    };\n    const handleStringChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-2\",\n                        children: \"Mental Status Examination\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-slate-600\",\n                        children: \"Systematic evaluation of the patient's current mental state.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Appearance & Behavior\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Appearance\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                        value: formData.appearance || \"\",\n                                        onValueChange: (value)=>handleStringChange(\"appearance\", value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                    placeholder: \"Select appearance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                children: APPEARANCE_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                        value: option,\n                                                        children: option\n                                                    }, option, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Behavior\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                        value: formData.behavior || \"\",\n                                        onValueChange: (value)=>handleStringChange(\"behavior\", value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                    placeholder: \"Select behavior\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                children: BEHAVIOR_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                        value: option,\n                                                        children: option\n                                                    }, option, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Attitude\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.attitude || \"\",\n                                        onChange: (e)=>handleStringChange(\"attitude\", e.target.value),\n                                        placeholder: \"Describe patient's attitude toward the interview\",\n                                        rows: 2\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Speech\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                            children: \"Rate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                            value: formData.speechRate || \"\",\n                                            onValueChange: (value)=>handleStringChange(\"speechRate\", value),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                        placeholder: \"Select rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                    children: SPEECH_RATE_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                            value: option,\n                                                            children: option\n                                                        }, option, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                            children: \"Volume\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                            value: formData.speechVolume || \"\",\n                                            onValueChange: (value)=>handleStringChange(\"speechVolume\", value),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                        placeholder: \"Select volume\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                    children: SPEECH_VOLUME_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                            value: option,\n                                                            children: option\n                                                        }, option, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                            children: \"Tone\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                            value: formData.speechTone || \"\",\n                                            onChange: (e)=>handleStringChange(\"speechTone\", e.target.value),\n                                            placeholder: \"Describe tone\",\n                                            rows: 1\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Mood & Affect\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                children: \"Mood\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                value: formData.mood || \"\",\n                                                onValueChange: (value)=>handleStringChange(\"mood\", value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                            placeholder: \"Select mood\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                        children: MOOD_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: option,\n                                                                children: option\n                                                            }, option, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                children: \"Affect\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                value: formData.affect || \"\",\n                                                onValueChange: (value)=>handleStringChange(\"affect\", value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                            placeholder: \"Select affect\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                        children: AFFECT_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: option,\n                                                                children: option\n                                                            }, option, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                children: \"Affect Range\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                                value: formData.affectRange || \"\",\n                                                onChange: (e)=>handleStringChange(\"affectRange\", e.target.value),\n                                                placeholder: \"Describe range (e.g., full, restricted)\",\n                                                rows: 1\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                children: \"Affect Intensity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                                value: formData.affectIntensity || \"\",\n                                                onChange: (e)=>handleStringChange(\"affectIntensity\", e.target.value),\n                                                placeholder: \"Describe intensity\",\n                                                rows: 1\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Thought Process & Content\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Thought Process\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                        value: formData.thoughtProcess || \"\",\n                                        onValueChange: (value)=>handleStringChange(\"thoughtProcess\", value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                    placeholder: \"Select thought process\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                children: THOUGHT_PROCESS_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                        value: option,\n                                                        children: option\n                                                    }, option, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Thought Content\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.thoughtContent || \"\",\n                                        onChange: (e)=>handleStringChange(\"thoughtContent\", e.target.value),\n                                        placeholder: \"Describe thought content, preoccupations, obsessions\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Perceptual Disturbances\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        className: \"text-base font-medium\",\n                                        children: \"Hallucinations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroup, {\n                                        value: ((_formData_hallucinations = formData.hallucinations) === null || _formData_hallucinations === void 0 ? void 0 : _formData_hallucinations.toString()) || \"\",\n                                        onValueChange: (value)=>handleBooleanChange(\"hallucinations\", value),\n                                        className: \"mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                        value: \"false\",\n                                                        id: \"hall-no\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"hall-no\",\n                                                        children: \"No\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                        value: \"true\",\n                                                        id: \"hall-yes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"hall-yes\",\n                                                        children: \"Yes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, this),\n                            formData.hallucinations && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Type of Hallucinations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.hallucinationType || \"\",\n                                        onChange: (e)=>handleStringChange(\"hallucinationType\", e.target.value),\n                                        placeholder: \"Describe type (auditory, visual, tactile, etc.)\",\n                                        rows: 2\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        className: \"text-base font-medium\",\n                                        children: \"Delusions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroup, {\n                                        value: ((_formData_delusions = formData.delusions) === null || _formData_delusions === void 0 ? void 0 : _formData_delusions.toString()) || \"\",\n                                        onValueChange: (value)=>handleBooleanChange(\"delusions\", value),\n                                        className: \"mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                        value: \"false\",\n                                                        id: \"del-no\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"del-no\",\n                                                        children: \"No\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                        value: \"true\",\n                                                        id: \"del-yes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"del-yes\",\n                                                        children: \"Yes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, this),\n                            formData.delusions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Type of Delusions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.delusionType || \"\",\n                                        onChange: (e)=>handleStringChange(\"delusionType\", e.target.value),\n                                        placeholder: \"Describe type (paranoid, grandiose, somatic, etc.)\",\n                                        rows: 2\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Cognitive Function\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                children: \"Orientation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                value: formData.orientation || \"\",\n                                                onValueChange: (value)=>handleStringChange(\"orientation\", value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                            placeholder: \"Select orientation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                        children: ORIENTATION_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: option,\n                                                                children: option\n                                                            }, option, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                children: \"Attention\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                                value: formData.attention || \"\",\n                                                onChange: (e)=>handleStringChange(\"attention\", e.target.value),\n                                                placeholder: \"Describe attention span\",\n                                                rows: 1\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                children: \"Concentration\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                                value: formData.concentration || \"\",\n                                                onChange: (e)=>handleStringChange(\"concentration\", e.target.value),\n                                                placeholder: \"Describe concentration ability\",\n                                                rows: 1\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                children: \"Memory\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                                value: formData.memory || \"\",\n                                                onChange: (e)=>handleStringChange(\"memory\", e.target.value),\n                                                placeholder: \"Describe memory function\",\n                                                rows: 1\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                lineNumber: 369,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Insight & Judgment\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                            children: \"Insight\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                            value: formData.insight || \"\",\n                                            onValueChange: (value)=>handleStringChange(\"insight\", value),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                        placeholder: \"Select insight level\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                    children: INSIGHT_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                            value: option,\n                                                            children: option\n                                                        }, option, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                            children: \"Judgment\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                            value: formData.judgment || \"\",\n                                            onValueChange: (value)=>handleStringChange(\"judgment\", value),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                        placeholder: \"Select judgment level\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                    children: JUDGMENT_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                            value: option,\n                                                            children: option\n                                                        }, option, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                        lineNumber: 434,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n                lineNumber: 430,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MentalStatusExamSection.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(MentalStatusExamSection, \"JPolC2XS0g7tYnXkmRHL767uIww=\");\n_c = MentalStatusExamSection;\nvar _c;\n$RefreshReg$(_c, \"MentalStatusExamSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/assessment/MentalStatusExamSection.tsx\n"));

/***/ })

});