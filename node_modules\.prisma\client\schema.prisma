// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Assessment {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Assessment metadata
  assessorName   String?
  assessmentDate DateTime @default(now())
  status         String   @default("in_progress") // in_progress, completed, draft

  // Related data
  demographics     Demographics?
  symptoms         SymptomAssessment[]
  riskAssessment   RiskAssessment?
  medicalHistory   MedicalHistory?
  mentalStatusExam MentalStatusExam?
  diagnoses        DiagnosisAssessment[]

  @@map("assessments")
}

model Demographics {
  id           String     @id @default(cuid())
  assessmentId String     @unique
  assessment   Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  // Basic Information
  firstName   String?
  lastName    String?
  dateOfBirth DateTime?
  age         Int?
  gender      String?

  // Contact Information
  phone   String?
  email   String?
  address String?
  city    String?
  state   String?
  zipCode String?

  // Demographics
  ethnicity       String?
  race            String?
  primaryLanguage String?
  maritalStatus   String?

  // Social Information
  education         String?
  occupation        String?
  employmentStatus  String?
  livingArrangement String?

  // Insurance/Financial
  insuranceType    String?
  emergencyContact String?
  emergencyPhone   String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("demographics")
}

model SymptomAssessment {
  id           String     @id @default(cuid())
  assessmentId String
  assessment   Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  symptomId String
  symptom   Symptom @relation(fields: [symptomId], references: [id])

  severity  String? // mild, moderate, severe
  duration  String? // days, weeks, months, years
  frequency String? // daily, weekly, monthly, rarely
  notes     String?

  createdAt DateTime @default(now())

  @@unique([assessmentId, symptomId])
  @@map("symptom_assessments")
}

model Symptom {
  id          String  @id @default(cuid())
  name        String  @unique
  category    String
  description String?

  assessments SymptomAssessment[]

  @@map("symptoms")
}

model RiskAssessment {
  id           String     @id @default(cuid())
  assessmentId String     @unique
  assessment   Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  // Suicide Risk
  suicidalIdeation       Boolean @default(false)
  suicidalPlan           Boolean @default(false)
  suicidalMeans          Boolean @default(false)
  suicidalAttemptHistory Boolean @default(false)
  suicidalRiskLevel      String? // low, moderate, high

  // Violence Risk
  homicidalIdeation Boolean @default(false)
  violenceHistory   Boolean @default(false)
  violenceRiskLevel String? // low, moderate, high

  // Self-harm
  selfHarmHistory Boolean @default(false)
  selfHarmRisk    String? // low, moderate, high

  // Substance Use
  substanceUseRisk String? // low, moderate, high

  // Additional notes
  riskFactors       String?
  protectiveFactors String?
  interventions     String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("risk_assessments")
}

model MedicalHistory {
  id           String     @id @default(cuid())
  assessmentId String     @unique
  assessment   Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  // Medical History
  currentMedications String?
  allergies          String?
  medicalConditions  String?
  surgicalHistory    String?

  // Psychiatric History
  previousPsychiatricTreatment Boolean @default(false)
  previousHospitalizations     String?
  previousMedications          String?
  familyPsychiatricHistory     String?

  // Substance Use History
  alcoholUse            String?
  drugUse               String?
  tobaccoUse            String?
  substanceAbuseHistory String?

  // Trauma History
  traumaHistory Boolean @default(false)
  traumaDetails String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("medical_history")
}

model MentalStatusExam {
  id           String     @id @default(cuid())
  assessmentId String     @unique
  assessment   Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  // Appearance
  appearance String?
  behavior   String?
  attitude   String?

  // Speech
  speechRate   String?
  speechVolume String?
  speechTone   String?

  // Mood and Affect
  mood            String?
  affect          String?
  affectRange     String?
  affectIntensity String?

  // Thought Process
  thoughtProcess String?
  thoughtContent String?

  // Perceptual Disturbances
  hallucinations    Boolean @default(false)
  hallucinationType String?
  delusions         Boolean @default(false)
  delusionType      String?

  // Cognitive Function
  orientation   String?
  attention     String?
  concentration String?
  memory        String?

  // Insight and Judgment
  insight  String?
  judgment String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("mental_status_exams")
}

model DiagnosisAssessment {
  id           String     @id @default(cuid())
  assessmentId String
  assessment   Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  diagnosisId String
  diagnosis   Diagnosis @relation(fields: [diagnosisId], references: [id])

  type       String // primary, secondary, rule_out
  confidence String? // definite, probable, possible
  notes      String?

  createdAt DateTime @default(now())

  @@unique([assessmentId, diagnosisId])
  @@map("diagnosis_assessments")
}

model Diagnosis {
  id          String  @id @default(cuid())
  code        String  @unique
  name        String
  category    String
  description String?

  assessments DiagnosisAssessment[]

  @@map("diagnoses")
}
