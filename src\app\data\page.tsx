"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Database, 
  Download, 
  ArrowLeft,
  Calendar,
  User,
  FileText,
  BarChart3
} from "lucide-react"
import Link from "next/link"

interface Assessment {
  id: string
  assessmentDate: string
  assessorName: string
  status: string
  createdAt: string
  demographics?: {
    firstName?: string
    lastName?: string
    age?: number
    gender?: string
  }
  _count: {
    symptoms: number
    diagnoses: number
  }
}

export default function DataPage() {
  const [assessments, setAssessments] = useState<Assessment[]>([])
  const [loading, setLoading] = useState(true)
  const [exporting, setExporting] = useState(false)

  useEffect(() => {
    fetchAssessments()
  }, [])

  const fetchAssessments = async () => {
    try {
      const response = await fetch('/api/assessments')
      if (response.ok) {
        const data = await response.json()
        setAssessments(data)
      }
    } catch (error) {
      console.error('Error fetching assessments:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleExport = async (format: 'csv' | 'json') => {
    setExporting(true)
    try {
      const response = await fetch(`/api/export?format=${format}`)
      
      if (!response.ok) {
        throw new Error('Failed to export data')
      }

      const blob = await response.blob()
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `psychiatric-assessments-${new Date().toISOString().split('T')[0]}.${format}`
      a.click()
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error exporting data:', error)
      alert('Failed to export data. Please try again.')
    } finally {
      setExporting(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-500">Completed</Badge>
      case 'in_progress':
        return <Badge variant="secondary">In Progress</Badge>
      case 'draft':
        return <Badge variant="outline">Draft</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="assessment-container py-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Link href="/">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-slate-900">Assessment Data</h1>
            <p className="text-sm text-slate-600">View and export collected assessment data for ML training</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button 
            onClick={() => handleExport('csv')} 
            disabled={exporting || assessments.length === 0}
            variant="outline"
          >
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Button 
            onClick={() => handleExport('json')} 
            disabled={exporting || assessments.length === 0}
            variant="outline"
          >
            <Download className="h-4 w-4 mr-2" />
            Export JSON
          </Button>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Database className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm text-slate-600">Total Assessments</p>
                <p className="text-2xl font-bold">{assessments.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm text-slate-600">Completed</p>
                <p className="text-2xl font-bold">
                  {assessments.filter(a => a.status === 'completed').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm text-slate-600">In Progress</p>
                <p className="text-2xl font-bold">
                  {assessments.filter(a => a.status === 'in_progress').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm text-slate-600">This Week</p>
                <p className="text-2xl font-bold">
                  {assessments.filter(a => {
                    const weekAgo = new Date()
                    weekAgo.setDate(weekAgo.getDate() - 7)
                    return new Date(a.createdAt) > weekAgo
                  }).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Assessment List */}
      <Card>
        <CardHeader>
          <CardTitle>Assessment Records</CardTitle>
          <CardDescription>
            All collected assessment data ready for ML training
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-slate-600">Loading assessments...</p>
            </div>
          ) : assessments.length === 0 ? (
            <div className="text-center py-8">
              <Database className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-600">No assessments found</p>
              <p className="text-sm text-slate-500 mt-1">
                <Link href="/assessment" className="text-blue-600 hover:underline">
                  Create your first assessment
                </Link>
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {assessments.map((assessment) => (
                <div key={assessment.id} className="border rounded-lg p-4 hover:bg-slate-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <User className="h-4 w-4 text-slate-500" />
                        <span className="font-medium">
                          {assessment.demographics?.firstName && assessment.demographics?.lastName
                            ? `${assessment.demographics.firstName} ${assessment.demographics.lastName}`
                            : assessment.assessorName || 'Anonymous'
                          }
                        </span>
                        {getStatusBadge(assessment.status)}
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-slate-600">
                        <div>
                          <span className="font-medium">Age:</span> {assessment.demographics?.age || 'N/A'}
                        </div>
                        <div>
                          <span className="font-medium">Gender:</span> {assessment.demographics?.gender || 'N/A'}
                        </div>
                        <div>
                          <span className="font-medium">Symptoms:</span> {assessment._count.symptoms}
                        </div>
                        <div>
                          <span className="font-medium">Diagnoses:</span> {assessment._count.diagnoses}
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-right text-sm text-slate-500">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3" />
                        <span>{formatDate(assessment.createdAt)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* ML Training Info */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>ML Training Data Format</CardTitle>
          <CardDescription>
            Information about the exported data structure for machine learning
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">CSV Export Features</h4>
              <ul className="text-sm text-slate-600 space-y-1">
                <li>• Flattened structure for easy ML processing</li>
                <li>• Categorical variables properly encoded</li>
                <li>• Boolean risk factors as binary features</li>
                <li>• Timestamp data for temporal analysis</li>
                <li>• Symptom and diagnosis counts as features</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">JSON Export Features</h4>
              <ul className="text-sm text-slate-600 space-y-1">
                <li>• Complete hierarchical data structure</li>
                <li>• Preserves all relationships and metadata</li>
                <li>• Detailed symptom and diagnosis information</li>
                <li>• Full text responses for NLP analysis</li>
                <li>• Export metadata and timestamps</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
