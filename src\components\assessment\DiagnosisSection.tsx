"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Plus, X } from "lucide-react"

interface DiagnosisData {
  primaryDiagnosis?: string
  primaryDiagnosisCode?: string
  secondaryDiagnoses?: Array<{
    diagnosis: string
    code: string
    type: string
  }>
  differentialDiagnoses?: string
  diagnosticImpression?: string
  treatmentRecommendations?: string
}

interface DiagnosisSectionProps {
  data: DiagnosisData
  onUpdate: (data: DiagnosisData) => void
}

// Common psychiatric diagnoses for quick selection
const COMMON_DIAGNOSES = [
  { code: "F32.9", name: "Major Depressive Disorder, Single Episode, Unspecified" },
  { code: "F33.9", name: "Major Depressive Disorder, Recurrent, Unspecified" },
  { code: "F41.1", name: "Generalized Anxiety Disorder" },
  { code: "F41.0", name: "Panic Disorder" },
  { code: "F43.10", name: "Post-Traumatic Stress Disorder, Unspecified" },
  { code: "F31.9", name: "Bipolar Disorder, Unspecified" },
  { code: "F20.9", name: "Schizophrenia, Unspecified" },
  { code: "F42.2", name: "Mixed Obsessional Thoughts and Acts" },
  { code: "F10.20", name: "Alcohol Use Disorder, Moderate" },
  { code: "F43.25", name: "Adjustment Disorder with Mixed Anxiety and Depressed Mood" },
  { code: "F60.3", name: "Borderline Personality Disorder" },
  { code: "F90.9", name: "Attention-Deficit/Hyperactivity Disorder, Unspecified" }
]

const DIAGNOSIS_TYPES = [
  { value: "primary", label: "Primary" },
  { value: "secondary", label: "Secondary" },
  { value: "rule_out", label: "Rule Out" }
]

const CONFIDENCE_LEVELS = [
  { value: "definite", label: "Definite" },
  { value: "probable", label: "Probable" },
  { value: "possible", label: "Possible" }
]

export default function DiagnosisSection({ data, onUpdate }: DiagnosisSectionProps) {
  const [formData, setFormData] = useState<DiagnosisData>(data || {
    secondaryDiagnoses: []
  })
  const [searchTerm, setSearchTerm] = useState("")
  const [filteredDiagnoses, setFilteredDiagnoses] = useState(COMMON_DIAGNOSES)

  useEffect(() => {
    onUpdate(formData)
  }, [formData, onUpdate])

  useEffect(() => {
    if (searchTerm) {
      const filtered = COMMON_DIAGNOSES.filter(
        diagnosis =>
          diagnosis.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          diagnosis.code.toLowerCase().includes(searchTerm.toLowerCase())
      )
      setFilteredDiagnoses(filtered)
    } else {
      setFilteredDiagnoses(COMMON_DIAGNOSES)
    }
  }, [searchTerm])

  const handleStringChange = (field: keyof DiagnosisData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handlePrimaryDiagnosisSelect = (diagnosis: typeof COMMON_DIAGNOSES[0]) => {
    setFormData(prev => ({
      ...prev,
      primaryDiagnosis: diagnosis.name,
      primaryDiagnosisCode: diagnosis.code
    }))
    setSearchTerm("")
  }

  const addSecondaryDiagnosis = () => {
    setFormData(prev => ({
      ...prev,
      secondaryDiagnoses: [
        ...(prev.secondaryDiagnoses || []),
        { diagnosis: "", code: "", type: "secondary" }
      ]
    }))
  }

  const updateSecondaryDiagnosis = (index: number, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      secondaryDiagnoses: prev.secondaryDiagnoses?.map((diag, i) =>
        i === index ? { ...diag, [field]: value } : diag
      )
    }))
  }

  const removeSecondaryDiagnosis = (index: number) => {
    setFormData(prev => ({
      ...prev,
      secondaryDiagnoses: prev.secondaryDiagnoses?.filter((_, i) => i !== index)
    }))
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-2">Diagnosis</h2>
        <p className="text-sm text-slate-600">Provide diagnostic formulation and treatment recommendations.</p>
      </div>

      {/* Primary Diagnosis */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Primary Diagnosis</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Search Diagnoses</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search by diagnosis name or ICD-10 code..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {searchTerm && (
            <div className="border rounded-lg max-h-48 overflow-y-auto">
              {filteredDiagnoses.map((diagnosis, index) => (
                <div
                  key={index}
                  className="p-3 hover:bg-gray-50 cursor-pointer border-b last:border-b-0"
                  onClick={() => handlePrimaryDiagnosisSelect(diagnosis)}
                >
                  <div className="font-medium text-sm">{diagnosis.code}</div>
                  <div className="text-sm text-gray-600">{diagnosis.name}</div>
                </div>
              ))}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Primary Diagnosis</Label>
              <Input
                value={formData.primaryDiagnosis || ''}
                onChange={(e) => handleStringChange('primaryDiagnosis', e.target.value)}
                placeholder="Enter primary diagnosis"
              />
            </div>
            <div className="space-y-2">
              <Label>ICD-10 Code</Label>
              <Input
                value={formData.primaryDiagnosisCode || ''}
                onChange={(e) => handleStringChange('primaryDiagnosisCode', e.target.value)}
                placeholder="Enter ICD-10 code"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Secondary Diagnoses */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Secondary Diagnoses</CardTitle>
            <Button onClick={addSecondaryDiagnosis} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Diagnosis
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {formData.secondaryDiagnoses?.map((diagnosis, index) => (
            <div key={index} className="border rounded-lg p-4 space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Secondary Diagnosis {index + 1}</h4>
                <Button
                  onClick={() => removeSecondaryDiagnosis(index)}
                  variant="outline"
                  size="sm"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>Diagnosis</Label>
                  <Input
                    value={diagnosis.diagnosis}
                    onChange={(e) => updateSecondaryDiagnosis(index, 'diagnosis', e.target.value)}
                    placeholder="Enter diagnosis"
                  />
                </div>
                <div className="space-y-2">
                  <Label>ICD-10 Code</Label>
                  <Input
                    value={diagnosis.code}
                    onChange={(e) => updateSecondaryDiagnosis(index, 'code', e.target.value)}
                    placeholder="Enter code"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Type</Label>
                  <Select
                    value={diagnosis.type}
                    onValueChange={(value) => updateSecondaryDiagnosis(index, 'type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      {DIAGNOSIS_TYPES.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          ))}

          {(!formData.secondaryDiagnoses || formData.secondaryDiagnoses.length === 0) && (
            <div className="text-center py-8 text-gray-500">
              No secondary diagnoses added. Click "Add Diagnosis" to add one.
            </div>
          )}
        </CardContent>
      </Card>

      {/* Clinical Impression */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Clinical Impression & Recommendations</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Differential Diagnoses</Label>
            <Textarea
              value={formData.differentialDiagnoses || ''}
              onChange={(e) => handleStringChange('differentialDiagnoses', e.target.value)}
              placeholder="List other diagnoses considered and ruled out"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label>Diagnostic Impression</Label>
            <Textarea
              value={formData.diagnosticImpression || ''}
              onChange={(e) => handleStringChange('diagnosticImpression', e.target.value)}
              placeholder="Provide overall diagnostic impression and rationale"
              rows={4}
            />
          </div>

          <div className="space-y-2">
            <Label>Treatment Recommendations</Label>
            <Textarea
              value={formData.treatmentRecommendations || ''}
              onChange={(e) => handleStringChange('treatmentRecommendations', e.target.value)}
              placeholder="Provide treatment recommendations including medications, therapy, follow-up care"
              rows={4}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
