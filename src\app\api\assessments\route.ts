import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Create a new assessment
    const assessment = await db.assessment.create({
      data: {
        assessorName: body.assessorName || 'Anonymous',
        status: 'in_progress'
      }
    })

    // Create demographics if provided
    if (body.demographics && Object.keys(body.demographics).length > 0) {
      await db.demographics.create({
        data: {
          assessmentId: assessment.id,
          ...body.demographics
        }
      })
    }

    // Create risk assessment if provided
    if (body.riskAssessment && Object.keys(body.riskAssessment).length > 0) {
      await db.riskAssessment.create({
        data: {
          assessmentId: assessment.id,
          ...body.riskAssessment
        }
      })
    }

    // Create medical history if provided
    if (body.medicalHistory && Object.keys(body.medicalHistory).length > 0) {
      await db.medicalHistory.create({
        data: {
          assessmentId: assessment.id,
          ...body.medicalHistory
        }
      })
    }

    // Create mental status exam if provided
    if (body.mentalStatusExam && Object.keys(body.mentalStatusExam).length > 0) {
      await db.mentalStatusExam.create({
        data: {
          assessmentId: assessment.id,
          ...body.mentalStatusExam
        }
      })
    }

    // Handle symptoms
    if (body.symptoms && body.symptoms.selectedSymptoms && body.symptoms.selectedSymptoms.length > 0) {
      for (const symptomName of body.symptoms.selectedSymptoms) {
        // Find or create symptom
        let symptom = await db.symptom.findFirst({
          where: { name: symptomName }
        })

        if (!symptom) {
          symptom = await db.symptom.create({
            data: {
              name: symptomName,
              category: 'Other',
              description: ''
            }
          })
        }

        // Create symptom assessment
        const symptomDetails = body.symptoms.symptomDetails?.[symptomName] || {}
        await db.symptomAssessment.create({
          data: {
            assessmentId: assessment.id,
            symptomId: symptom.id,
            severity: symptomDetails.severity,
            duration: symptomDetails.duration,
            frequency: symptomDetails.frequency,
            notes: symptomDetails.notes
          }
        })
      }
    }

    // Handle diagnoses
    if (body.diagnosis) {
      // Primary diagnosis
      if (body.diagnosis.primaryDiagnosis && body.diagnosis.primaryDiagnosisCode) {
        let diagnosis = await db.diagnosis.findFirst({
          where: { code: body.diagnosis.primaryDiagnosisCode }
        })

        if (!diagnosis) {
          diagnosis = await db.diagnosis.create({
            data: {
              code: body.diagnosis.primaryDiagnosisCode,
              name: body.diagnosis.primaryDiagnosis,
              category: 'Other',
              description: ''
            }
          })
        }

        await db.diagnosisAssessment.create({
          data: {
            assessmentId: assessment.id,
            diagnosisId: diagnosis.id,
            type: 'primary',
            confidence: 'definite'
          }
        })
      }

      // Secondary diagnoses
      if (body.diagnosis.secondaryDiagnoses && body.diagnosis.secondaryDiagnoses.length > 0) {
        for (const secDiag of body.diagnosis.secondaryDiagnoses) {
          if (secDiag.diagnosis && secDiag.code) {
            let diagnosis = await db.diagnosis.findFirst({
              where: { code: secDiag.code }
            })

            if (!diagnosis) {
              diagnosis = await db.diagnosis.create({
                data: {
                  code: secDiag.code,
                  name: secDiag.diagnosis,
                  category: 'Other',
                  description: ''
                }
              })
            }

            await db.diagnosisAssessment.create({
              data: {
                assessmentId: assessment.id,
                diagnosisId: diagnosis.id,
                type: secDiag.type || 'secondary',
                confidence: 'probable'
              }
            })
          }
        }
      }
    }

    return NextResponse.json({ 
      success: true, 
      assessmentId: assessment.id 
    })

  } catch (error) {
    console.error('Error saving assessment:', error)
    return NextResponse.json(
      { error: 'Failed to save assessment' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const assessmentId = searchParams.get('id')

    if (assessmentId) {
      // Get specific assessment
      const assessment = await db.assessment.findUnique({
        where: { id: assessmentId },
        include: {
          demographics: true,
          riskAssessment: true,
          medicalHistory: true,
          mentalStatusExam: true,
          symptoms: {
            include: {
              symptom: true
            }
          },
          diagnoses: {
            include: {
              diagnosis: true
            }
          }
        }
      })

      if (!assessment) {
        return NextResponse.json(
          { error: 'Assessment not found' },
          { status: 404 }
        )
      }

      return NextResponse.json(assessment)
    } else {
      // Get all assessments
      const assessments = await db.assessment.findMany({
        include: {
          demographics: true,
          _count: {
            select: {
              symptoms: true,
              diagnoses: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      })

      return NextResponse.json(assessments)
    }

  } catch (error) {
    console.error('Error fetching assessments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch assessments' },
      { status: 500 }
    )
  }
}
