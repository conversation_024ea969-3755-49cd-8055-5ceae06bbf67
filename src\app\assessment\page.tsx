"use client"

import { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Brain, 
  Users, 
  Shield, 
  FileText, 
  BarChart3, 
  Database,
  Save,
  Download,
  ArrowLeft,
  CheckCircle
} from "lucide-react"
import Link from "next/link"

// Import assessment sections (we'll create these next)
import DemographicsSection from "@/components/assessment/DemographicsSection"
import SymptomsSection from "@/components/assessment/SymptomsSection"
import RiskAssessmentSection from "@/components/assessment/RiskAssessmentSection"
import MedicalHistorySection from "@/components/assessment/MedicalHistorySection"
import MentalStatusExamSection from "@/components/assessment/MentalStatusExamSection"
import DiagnosisSection from "@/components/assessment/DiagnosisSection"

interface AssessmentData {
  demographics: any
  symptoms: any
  riskAssessment: any
  medicalHistory: any
  mentalStatusExam: any
  diagnosis: any
}

const ASSESSMENT_SECTIONS = [
  { id: "demographics", label: "Demographics", icon: Users, component: DemographicsSection },
  { id: "symptoms", label: "Symptoms", icon: Brain, component: SymptomsSection },
  { id: "risk", label: "Risk Assessment", icon: Shield, component: RiskAssessmentSection },
  { id: "history", label: "Medical History", icon: FileText, component: MedicalHistorySection },
  { id: "mental-status", label: "Mental Status", icon: BarChart3, component: MentalStatusExamSection },
  { id: "diagnosis", label: "Diagnosis", icon: Database, component: DiagnosisSection },
]

export default function AssessmentPage() {
  const [activeTab, setActiveTab] = useState("demographics")
  const [assessmentData, setAssessmentData] = useState<AssessmentData>({
    demographics: {},
    symptoms: {},
    riskAssessment: {},
    medicalHistory: {},
    mentalStatusExam: {},
    diagnosis: {}
  })
  const [completedSections, setCompletedSections] = useState<Set<string>>(new Set())
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [isSaving, setIsSaving] = useState(false)

  // Calculate progress
  const progress = (completedSections.size / ASSESSMENT_SECTIONS.length) * 100

  // Auto-save functionality (debounced)
  useEffect(() => {
    const saveTimer = setTimeout(() => {
      if (Object.keys(assessmentData.demographics).length > 0 || 
          Object.keys(assessmentData.symptoms).length > 0) {
        handleAutoSave()
      }
    }, 2000) // 2-second debounce

    return () => clearTimeout(saveTimer)
  }, [assessmentData])

  // Load data from localStorage on mount
  useEffect(() => {
    const savedData = localStorage.getItem('psychiatric-assessment-data')
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData)
        setAssessmentData(parsed.data || assessmentData)
        setCompletedSections(new Set(parsed.completedSections || []))
        setLastSaved(parsed.lastSaved ? new Date(parsed.lastSaved) : null)
      } catch (error) {
        console.error('Error loading saved data:', error)
      }
    }
  }, [])

  const handleAutoSave = async () => {
    setIsSaving(true)
    try {
      // Save to localStorage as backup
      const dataToSave = {
        data: assessmentData,
        completedSections: Array.from(completedSections),
        lastSaved: new Date().toISOString()
      }
      localStorage.setItem('psychiatric-assessment-data', JSON.stringify(dataToSave))

      // Save to database via API
      const response = await fetch('/api/assessments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(assessmentData)
      })

      if (!response.ok) {
        throw new Error('Failed to save to database')
      }

      setLastSaved(new Date())
    } catch (error) {
      console.error('Error saving data:', error)
      // Still update lastSaved for localStorage backup
      setLastSaved(new Date())
    } finally {
      setIsSaving(false)
    }
  }

  const handleSectionUpdate = (sectionId: string, data: any) => {
    setAssessmentData(prev => ({
      ...prev,
      [sectionId]: data
    }))
    
    // Mark section as completed if it has required data
    if (data && Object.keys(data).length > 0) {
      setCompletedSections(prev => new Set([...prev, sectionId]))
    }
  }

  const handleExportData = async (format: 'csv' | 'json') => {
    try {
      const response = await fetch(`/api/export?format=${format}`)

      if (!response.ok) {
        throw new Error('Failed to export data')
      }

      const blob = await response.blob()
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `psychiatric-assessments-${new Date().toISOString().split('T')[0]}.${format}`
      a.click()
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error exporting data:', error)
      // Fallback to local export
      const exportData = {
        ...assessmentData,
        metadata: {
          exportDate: new Date().toISOString(),
          completedSections: Array.from(completedSections),
          progress: progress
        }
      }

      if (format === 'json') {
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `psychiatric-assessment-${new Date().toISOString().split('T')[0]}.json`
        a.click()
        URL.revokeObjectURL(url)
      }
    }
  }

  const flattenObjectForCSV = (obj: any, prefix = ''): any => {
    let flattened: any = {}
    for (const key in obj) {
      if (obj[key] !== null && typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
        Object.assign(flattened, flattenObjectForCSV(obj[key], prefix + key + '_'))
      } else {
        flattened[prefix + key] = obj[key]
      }
    }
    return flattened
  }

  const convertToCSV = (data: any): string => {
    const headers = Object.keys(data)
    const values = Object.values(data)
    return [headers.join(','), values.join(',')].join('\n')
  }

  return (
    <div className="assessment-container py-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Link href="/">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-slate-900">Psychiatric Assessment</h1>
            <p className="text-sm text-slate-600">Complete all sections for comprehensive evaluation</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Auto-save indicator */}
          <div className="autosave-indicator">
            {isSaving ? (
              <>
                <Save className="h-4 w-4 animate-spin" />
                <span>Saving...</span>
              </>
            ) : lastSaved ? (
              <>
                <CheckCircle className="h-4 w-4" />
                <span>Saved {lastSaved.toLocaleTimeString()}</span>
              </>
            ) : null}
          </div>
          
          {/* Export buttons */}
          <Button variant="outline" size="sm" onClick={() => handleExportData('json')}>
            <Download className="h-4 w-4 mr-2" />
            Export JSON
          </Button>
          <Button variant="outline" size="sm" onClick={() => handleExportData('csv')}>
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
        </div>
      </div>

      {/* Progress indicator */}
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Assessment Progress</CardTitle>
            <span className="text-sm text-slate-600">{completedSections.size} of {ASSESSMENT_SECTIONS.length} sections completed</span>
          </div>
        </CardHeader>
        <CardContent>
          <Progress value={progress} className="w-full" />
          <div className="flex justify-between mt-2 text-xs text-slate-500">
            <span>0%</span>
            <span>{Math.round(progress)}% Complete</span>
            <span>100%</span>
          </div>
        </CardContent>
      </Card>

      {/* Main assessment interface */}
      <Card>
        <CardContent className="p-0">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <div className="border-b">
              <TabsList className="grid w-full grid-cols-6 h-auto p-1">
                {ASSESSMENT_SECTIONS.map((section) => {
                  const Icon = section.icon
                  const isCompleted = completedSections.has(section.id)
                  return (
                    <TabsTrigger
                      key={section.id}
                      value={section.id}
                      className="flex flex-col items-center space-y-1 p-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                    >
                      <div className="flex items-center space-x-1">
                        <Icon className="h-4 w-4" />
                        {isCompleted && <CheckCircle className="h-3 w-3 text-green-500" />}
                      </div>
                      <span className="text-xs font-medium">{section.label}</span>
                    </TabsTrigger>
                  )
                })}
              </TabsList>
            </div>

            {ASSESSMENT_SECTIONS.map((section) => {
              const Component = section.component
              return (
                <TabsContent key={section.id} value={section.id} className="p-6">
                  <Component
                    data={assessmentData[section.id as keyof AssessmentData]}
                    onUpdate={(data: any) => handleSectionUpdate(section.id, data)}
                  />
                </TabsContent>
              )
            })}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
