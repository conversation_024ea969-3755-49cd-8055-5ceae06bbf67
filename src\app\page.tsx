"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  Brain,
  Zap,
  Shield,
  Database,
  BarChart3,
  Users,
  ArrowRight,
  CheckCircle,
  Clock,
  FileText
} from "lucide-react"
import Link from "next/link"

export default function Home() {
  const [isStarting, setIsStarting] = useState(false)

  const handleStartAssessment = () => {
    setIsStarting(true)
    // Navigate to assessment page
    window.location.href = "/assessment"
  }

  return (
    <div className="assessment-container py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <div className="flex items-center justify-center mb-4">
          <Brain className="h-12 w-12 text-blue-600 mr-3" />
          <h1 className="text-4xl font-bold text-slate-900">
            Psychiatric Assessment System
          </h1>
        </div>
        <p className="text-xl text-slate-600 max-w-3xl mx-auto">
          Fast, reliable, and optimized for ML training data collection
        </p>
      </div>

      {/* Main Content */}
      <div className="grid lg:grid-cols-3 gap-8 mb-12">
        {/* Left Column - Features */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Zap className="h-5 w-5 mr-2 text-yellow-500" />
                Key Features
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="font-semibold">Lightning Fast</h3>
                      <p className="text-sm text-slate-600">Debounced 2-second autosave with optimized performance</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Shield className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="font-semibold">Data Protection</h3>
                      <p className="text-sm text-slate-600">Local storage backup prevents data loss</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Database className="h-5 w-5 text-purple-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="font-semibold">ML-Ready Export</h3>
                      <p className="text-sm text-slate-600">CSV and JSON export for machine learning</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <Users className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="font-semibold">Predefined Options</h3>
                      <p className="text-sm text-slate-600">Ready-to-click education, occupation, and living options</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <BarChart3 className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="font-semibold">Smart Diagnosis</h3>
                      <p className="text-sm text-slate-600">Searchable diagnosis database with filtering</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Clock className="h-5 w-5 text-indigo-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="font-semibold">Real-time Progress</h3>
                      <p className="text-sm text-slate-600">Track completion status across all sections</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Start Card */}
        <div>
          <Card className="h-full">
            <CardHeader>
              <CardTitle>Get Started</CardTitle>
              <CardDescription>
                Begin a new psychiatric assessment session
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                  <span className="text-sm font-medium">Assessment Sections</span>
                  <span className="text-sm bg-slate-200 px-2 py-1 rounded">6</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                  <span className="text-sm font-medium">Estimated Time</span>
                  <span className="text-sm bg-slate-200 px-2 py-1 rounded">15-30 min</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                  <span className="text-sm font-medium">Auto-save</span>
                  <span className="text-sm bg-green-100 text-green-800 px-2 py-1 rounded">Enabled</span>
                </div>
              </div>
              
              <Button
                onClick={handleStartAssessment}
                disabled={isStarting}
                className="w-full mb-3"
                size="lg"
              >
                {isStarting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Starting...
                  </>
                ) : (
                  <>
                    Start Assessment
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>

              <Link href="/data">
                <Button variant="outline" className="w-full" size="lg">
                  <Database className="mr-2 h-4 w-4" />
                  View Data & Export
                </Button>
              </Link>
              
              <div className="text-xs text-slate-500 text-center">
                All data is automatically saved and backed up
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Assessment Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Assessment Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="sections" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="sections">Sections</TabsTrigger>
              <TabsTrigger value="features">Features</TabsTrigger>
              <TabsTrigger value="export">Data Export</TabsTrigger>
            </TabsList>
            
            <TabsContent value="sections" className="mt-6">
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[
                  { title: "Demographics", desc: "Patient information and background", icon: Users },
                  { title: "Symptoms", desc: "Comprehensive symptom assessment", icon: Brain },
                  { title: "Risk Assessment", desc: "Safety and risk evaluation", icon: Shield },
                  { title: "History", desc: "Medical and psychiatric history", icon: FileText },
                  { title: "Mental Status Exam", desc: "Current mental state evaluation", icon: BarChart3 },
                  { title: "Diagnosis", desc: "Diagnostic formulation and coding", icon: Database }
                ].map((section, index) => (
                  <div key={index} className="p-4 border rounded-lg hover:bg-slate-50 transition-colors">
                    <div className="flex items-center mb-2">
                      <section.icon className="h-5 w-5 text-blue-600 mr-2" />
                      <h3 className="font-semibold">{section.title}</h3>
                    </div>
                    <p className="text-sm text-slate-600">{section.desc}</p>
                  </div>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="features" className="mt-6">
              <div className="space-y-4">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-semibold mb-3">Performance Optimizations</h3>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Debounced autosave (2 seconds)</li>
                      <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Virtualized symptom selection</li>
                      <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Optimized state management</li>
                      <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Efficient data persistence</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-3">User Experience</h3>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Intuitive form navigation</li>
                      <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Real-time progress tracking</li>
                      <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Predefined dropdown options</li>
                      <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Responsive design</li>
                    </ul>
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="export" className="mt-6">
              <div className="space-y-4">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-semibold mb-3">Export Formats</h3>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />CSV format for ML training</li>
                      <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />JSON format for applications</li>
                      <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Structured data format</li>
                      <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Metadata included</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-3">Data Quality</h3>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Validated and clean data</li>
                      <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Consistent formatting</li>
                      <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Complete field coverage</li>
                      <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Timestamp tracking</li>
                    </ul>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
