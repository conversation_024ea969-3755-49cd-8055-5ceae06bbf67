import { db } from '@/lib/db'

// Mock PrismaClient
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    $connect: jest.fn(),
    $disconnect: jest.fn(),
    assessment: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    symptom: {
      findMany: jest.fn(),
    },
    diagnosis: {
      findMany: jest.fn(),
    },
  })),
}))

describe('Database Connection', () => {
  it('exports a database instance', () => {
    expect(db).toBeDefined()
    expect(typeof db).toBe('object')
  })

  it('has the expected methods', () => {
    expect(db.assessment).toBeDefined()
    expect(db.symptom).toBeDefined()
    expect(db.diagnosis).toBeDefined()
  })
})

describe('Database Operations', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('can create an assessment', async () => {
    const mockAssessment = {
      id: 'test-id',
      createdAt: new Date(),
      updatedAt: new Date(),
      assessorName: 'Test Assessor',
      assessmentDate: new Date(),
      status: 'in_progress'
    }

    ;(db.assessment.create as jest.Mock).mockResolvedValue(mockAssessment)

    const result = await db.assessment.create({
      data: {
        assessorName: 'Test Assessor',
        status: 'in_progress'
      }
    })

    expect(result).toEqual(mockAssessment)
    expect(db.assessment.create).toHaveBeenCalledWith({
      data: {
        assessorName: 'Test Assessor',
        status: 'in_progress'
      }
    })
  })

  it('can fetch symptoms', async () => {
    const mockSymptoms = [
      { id: '1', name: 'Anxiety', category: 'Anxiety', description: 'Test anxiety' },
      { id: '2', name: 'Depression', category: 'Mood', description: 'Test depression' }
    ]

    ;(db.symptom.findMany as jest.Mock).mockResolvedValue(mockSymptoms)

    const result = await db.symptom.findMany()

    expect(result).toEqual(mockSymptoms)
    expect(db.symptom.findMany).toHaveBeenCalled()
  })

  it('can fetch diagnoses', async () => {
    const mockDiagnoses = [
      { id: '1', code: 'F32.9', name: 'Major Depression', category: 'Mood Disorders' },
      { id: '2', code: 'F41.1', name: 'GAD', category: 'Anxiety Disorders' }
    ]

    ;(db.diagnosis.findMany as jest.Mock).mockResolvedValue(mockDiagnoses)

    const result = await db.diagnosis.findMany()

    expect(result).toEqual(mockDiagnoses)
    expect(db.diagnosis.findMany).toHaveBeenCalled()
  })
})
