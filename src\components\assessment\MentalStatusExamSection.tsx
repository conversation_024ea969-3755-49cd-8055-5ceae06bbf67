"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface MentalStatusExamData {
  appearance?: string
  behavior?: string
  attitude?: string
  speechRate?: string
  speechVolume?: string
  speechTone?: string
  mood?: string
  affect?: string
  affectRange?: string
  affectIntensity?: string
  thoughtProcess?: string
  thoughtContent?: string
  hallucinations?: boolean
  hallucinationType?: string
  delusions?: boolean
  delusionType?: string
  orientation?: string
  attention?: string
  concentration?: string
  memory?: string
  insight?: string
  judgment?: string
}

interface MentalStatusExamSectionProps {
  data: MentalStatusExamData
  onUpdate: (data: MentalStatusExamData) => void
}

const APPEARANCE_OPTIONS = ["Well-groomed", "Disheveled", "Appropriate dress", "Inappropriate dress", "Poor hygiene"]
const BEHAVIOR_OPTIONS = ["Calm", "Agitated", "Restless", "Withdrawn", "Cooperative", "Uncooperative"]
const SPEECH_RATE_OPTIONS = ["Normal", "Rapid", "Slow", "Pressured"]
const SPEECH_VOLUME_OPTIONS = ["Normal", "Loud", "Soft", "Whispered"]
const MOOD_OPTIONS = ["Euthymic", "Depressed", "Anxious", "Irritable", "Euphoric", "Angry"]
const AFFECT_OPTIONS = ["Appropriate", "Flat", "Blunted", "Labile", "Inappropriate"]
const THOUGHT_PROCESS_OPTIONS = ["Linear", "Tangential", "Circumstantial", "Flight of ideas", "Loose associations"]
const ORIENTATION_OPTIONS = ["Oriented x3", "Oriented x2", "Oriented x1", "Disoriented"]
const INSIGHT_OPTIONS = ["Good", "Fair", "Poor", "Absent"]
const JUDGMENT_OPTIONS = ["Good", "Fair", "Poor", "Impaired"]

export default function MentalStatusExamSection({ data, onUpdate }: MentalStatusExamSectionProps) {
  const [formData, setFormData] = useState<MentalStatusExamData>(data || {})

  useEffect(() => {
    onUpdate(formData)
  }, [formData, onUpdate])

  const handleBooleanChange = (field: keyof MentalStatusExamData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value === 'true'
    }))
  }

  const handleStringChange = (field: keyof MentalStatusExamData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-2">Mental Status Examination</h2>
        <p className="text-sm text-slate-600">Systematic evaluation of the patient's current mental state.</p>
      </div>

      {/* Appearance and Behavior */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Appearance & Behavior</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Appearance</Label>
            <Select
              value={formData.appearance || ''}
              onValueChange={(value) => handleStringChange('appearance', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select appearance" />
              </SelectTrigger>
              <SelectContent>
                {APPEARANCE_OPTIONS.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Behavior</Label>
            <Select
              value={formData.behavior || ''}
              onValueChange={(value) => handleStringChange('behavior', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select behavior" />
              </SelectTrigger>
              <SelectContent>
                {BEHAVIOR_OPTIONS.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Attitude</Label>
            <Textarea
              value={formData.attitude || ''}
              onChange={(e) => handleStringChange('attitude', e.target.value)}
              placeholder="Describe patient's attitude toward the interview"
              rows={2}
            />
          </div>
        </CardContent>
      </Card>

      {/* Speech */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Speech</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Rate</Label>
              <Select
                value={formData.speechRate || ''}
                onValueChange={(value) => handleStringChange('speechRate', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select rate" />
                </SelectTrigger>
                <SelectContent>
                  {SPEECH_RATE_OPTIONS.map((option) => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Volume</Label>
              <Select
                value={formData.speechVolume || ''}
                onValueChange={(value) => handleStringChange('speechVolume', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select volume" />
                </SelectTrigger>
                <SelectContent>
                  {SPEECH_VOLUME_OPTIONS.map((option) => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Tone</Label>
              <Textarea
                value={formData.speechTone || ''}
                onChange={(e) => handleStringChange('speechTone', e.target.value)}
                placeholder="Describe tone"
                rows={1}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Mood and Affect */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Mood & Affect</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Mood</Label>
              <Select
                value={formData.mood || ''}
                onValueChange={(value) => handleStringChange('mood', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select mood" />
                </SelectTrigger>
                <SelectContent>
                  {MOOD_OPTIONS.map((option) => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Affect</Label>
              <Select
                value={formData.affect || ''}
                onValueChange={(value) => handleStringChange('affect', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select affect" />
                </SelectTrigger>
                <SelectContent>
                  {AFFECT_OPTIONS.map((option) => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Affect Range</Label>
              <Textarea
                value={formData.affectRange || ''}
                onChange={(e) => handleStringChange('affectRange', e.target.value)}
                placeholder="Describe range (e.g., full, restricted)"
                rows={1}
              />
            </div>

            <div className="space-y-2">
              <Label>Affect Intensity</Label>
              <Textarea
                value={formData.affectIntensity || ''}
                onChange={(e) => handleStringChange('affectIntensity', e.target.value)}
                placeholder="Describe intensity"
                rows={1}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Thought Process and Content */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Thought Process & Content</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Thought Process</Label>
            <Select
              value={formData.thoughtProcess || ''}
              onValueChange={(value) => handleStringChange('thoughtProcess', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select thought process" />
              </SelectTrigger>
              <SelectContent>
                {THOUGHT_PROCESS_OPTIONS.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Thought Content</Label>
            <Textarea
              value={formData.thoughtContent || ''}
              onChange={(e) => handleStringChange('thoughtContent', e.target.value)}
              placeholder="Describe thought content, preoccupations, obsessions"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Perceptual Disturbances */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Perceptual Disturbances</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-base font-medium">Hallucinations</Label>
            <RadioGroup
              value={formData.hallucinations?.toString() || ''}
              onValueChange={(value) => handleBooleanChange('hallucinations', value)}
              className="mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="false" id="hall-no" />
                <Label htmlFor="hall-no">No</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="true" id="hall-yes" />
                <Label htmlFor="hall-yes">Yes</Label>
              </div>
            </RadioGroup>
          </div>

          {formData.hallucinations && (
            <div className="space-y-2">
              <Label>Type of Hallucinations</Label>
              <Textarea
                value={formData.hallucinationType || ''}
                onChange={(e) => handleStringChange('hallucinationType', e.target.value)}
                placeholder="Describe type (auditory, visual, tactile, etc.)"
                rows={2}
              />
            </div>
          )}

          <div>
            <Label className="text-base font-medium">Delusions</Label>
            <RadioGroup
              value={formData.delusions?.toString() || ''}
              onValueChange={(value) => handleBooleanChange('delusions', value)}
              className="mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="false" id="del-no" />
                <Label htmlFor="del-no">No</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="true" id="del-yes" />
                <Label htmlFor="del-yes">Yes</Label>
              </div>
            </RadioGroup>
          </div>

          {formData.delusions && (
            <div className="space-y-2">
              <Label>Type of Delusions</Label>
              <Textarea
                value={formData.delusionType || ''}
                onChange={(e) => handleStringChange('delusionType', e.target.value)}
                placeholder="Describe type (paranoid, grandiose, somatic, etc.)"
                rows={2}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Cognitive Function */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Cognitive Function</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Orientation</Label>
              <Select
                value={formData.orientation || ''}
                onValueChange={(value) => handleStringChange('orientation', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select orientation" />
                </SelectTrigger>
                <SelectContent>
                  {ORIENTATION_OPTIONS.map((option) => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Attention</Label>
              <Textarea
                value={formData.attention || ''}
                onChange={(e) => handleStringChange('attention', e.target.value)}
                placeholder="Describe attention span"
                rows={1}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Concentration</Label>
              <Textarea
                value={formData.concentration || ''}
                onChange={(e) => handleStringChange('concentration', e.target.value)}
                placeholder="Describe concentration ability"
                rows={1}
              />
            </div>

            <div className="space-y-2">
              <Label>Memory</Label>
              <Textarea
                value={formData.memory || ''}
                onChange={(e) => handleStringChange('memory', e.target.value)}
                placeholder="Describe memory function"
                rows={1}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Insight and Judgment */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Insight & Judgment</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Insight</Label>
              <Select
                value={formData.insight || ''}
                onValueChange={(value) => handleStringChange('insight', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select insight level" />
                </SelectTrigger>
                <SelectContent>
                  {INSIGHT_OPTIONS.map((option) => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Judgment</Label>
              <Select
                value={formData.judgment || ''}
                onValueChange={(value) => handleStringChange('judgment', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select judgment level" />
                </SelectTrigger>
                <SelectContent>
                  {JUDGMENT_OPTIONS.map((option) => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
