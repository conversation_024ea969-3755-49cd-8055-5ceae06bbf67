"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RISK_LEVELS } from "@/lib/constants"
import { AlertTriangle } from "lucide-react"

interface RiskAssessmentData {
  // Suicide Risk
  suicidalIdeation?: boolean
  suicidalPlan?: boolean
  suicidalMeans?: boolean
  suicidalAttemptHistory?: boolean
  suicidalRiskLevel?: string
  
  // Violence Risk
  homicidalIdeation?: boolean
  violenceHistory?: boolean
  violenceRiskLevel?: string
  
  // Self-harm
  selfHarmHistory?: boolean
  selfHarmRisk?: string
  
  // Substance Use
  substanceUseRisk?: string
  
  // Additional notes
  riskFactors?: string
  protectiveFactors?: string
  interventions?: string
}

interface RiskAssessmentSectionProps {
  data: RiskAssessmentData
  onUpdate: (data: RiskAssessmentData) => void
}

export default function RiskAssessmentSection({ data, onUpdate }: RiskAssessmentSectionProps) {
  const [formData, setFormData] = useState<RiskAssessmentData>(data || {})

  useEffect(() => {
    onUpdate(formData)
  }, [formData, onUpdate])

  const handleBooleanChange = (field: keyof RiskAssessmentData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value === 'true'
    }))
  }

  const handleStringChange = (field: keyof RiskAssessmentData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const hasHighRisk = formData.suicidalRiskLevel === 'high' || 
                     formData.violenceRiskLevel === 'high' || 
                     formData.selfHarmRisk === 'high'

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-2">Risk Assessment</h2>
        <p className="text-sm text-slate-600">Evaluate potential risks for safety and intervention planning.</p>
        {hasHighRisk && (
          <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <span className="text-sm text-red-800 font-medium">High risk factors identified - immediate intervention may be required</span>
          </div>
        )}
      </div>

      {/* Suicide Risk Assessment */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Suicide Risk Assessment</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label className="text-base font-medium">Suicidal Ideation</Label>
              <RadioGroup
                value={formData.suicidalIdeation?.toString() || ''}
                onValueChange={(value) => handleBooleanChange('suicidalIdeation', value)}
                className="mt-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="false" id="si-no" />
                  <Label htmlFor="si-no">No</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="true" id="si-yes" />
                  <Label htmlFor="si-yes">Yes</Label>
                </div>
              </RadioGroup>
            </div>

            <div>
              <Label className="text-base font-medium">Suicidal Plan</Label>
              <RadioGroup
                value={formData.suicidalPlan?.toString() || ''}
                onValueChange={(value) => handleBooleanChange('suicidalPlan', value)}
                className="mt-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="false" id="sp-no" />
                  <Label htmlFor="sp-no">No</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="true" id="sp-yes" />
                  <Label htmlFor="sp-yes">Yes</Label>
                </div>
              </RadioGroup>
            </div>

            <div>
              <Label className="text-base font-medium">Access to Means</Label>
              <RadioGroup
                value={formData.suicidalMeans?.toString() || ''}
                onValueChange={(value) => handleBooleanChange('suicidalMeans', value)}
                className="mt-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="false" id="sm-no" />
                  <Label htmlFor="sm-no">No</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="true" id="sm-yes" />
                  <Label htmlFor="sm-yes">Yes</Label>
                </div>
              </RadioGroup>
            </div>

            <div>
              <Label className="text-base font-medium">History of Suicide Attempts</Label>
              <RadioGroup
                value={formData.suicidalAttemptHistory?.toString() || ''}
                onValueChange={(value) => handleBooleanChange('suicidalAttemptHistory', value)}
                className="mt-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="false" id="sah-no" />
                  <Label htmlFor="sah-no">No</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="true" id="sah-yes" />
                  <Label htmlFor="sah-yes">Yes</Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <Label>Overall Suicide Risk Level</Label>
              <Select
                value={formData.suicidalRiskLevel || ''}
                onValueChange={(value) => handleStringChange('suicidalRiskLevel', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select risk level" />
                </SelectTrigger>
                <SelectContent>
                  {RISK_LEVELS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Violence Risk Assessment */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Violence Risk Assessment</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <Label className="text-base font-medium">Homicidal Ideation</Label>
            <RadioGroup
              value={formData.homicidalIdeation?.toString() || ''}
              onValueChange={(value) => handleBooleanChange('homicidalIdeation', value)}
              className="mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="false" id="hi-no" />
                <Label htmlFor="hi-no">No</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="true" id="hi-yes" />
                <Label htmlFor="hi-yes">Yes</Label>
              </div>
            </RadioGroup>
          </div>

          <div>
            <Label className="text-base font-medium">History of Violence</Label>
            <RadioGroup
              value={formData.violenceHistory?.toString() || ''}
              onValueChange={(value) => handleBooleanChange('violenceHistory', value)}
              className="mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="false" id="vh-no" />
                <Label htmlFor="vh-no">No</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="true" id="vh-yes" />
                <Label htmlFor="vh-yes">Yes</Label>
              </div>
            </RadioGroup>
          </div>

          <div className="space-y-2">
            <Label>Violence Risk Level</Label>
            <Select
              value={formData.violenceRiskLevel || ''}
              onValueChange={(value) => handleStringChange('violenceRiskLevel', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select risk level" />
              </SelectTrigger>
              <SelectContent>
                {RISK_LEVELS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Self-Harm and Substance Use */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Self-Harm & Substance Use Risk</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <Label className="text-base font-medium">History of Self-Harm</Label>
            <RadioGroup
              value={formData.selfHarmHistory?.toString() || ''}
              onValueChange={(value) => handleBooleanChange('selfHarmHistory', value)}
              className="mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="false" id="shh-no" />
                <Label htmlFor="shh-no">No</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="true" id="shh-yes" />
                <Label htmlFor="shh-yes">Yes</Label>
              </div>
            </RadioGroup>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Self-Harm Risk Level</Label>
              <Select
                value={formData.selfHarmRisk || ''}
                onValueChange={(value) => handleStringChange('selfHarmRisk', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select risk level" />
                </SelectTrigger>
                <SelectContent>
                  {RISK_LEVELS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Substance Use Risk Level</Label>
              <Select
                value={formData.substanceUseRisk || ''}
                onValueChange={(value) => handleStringChange('substanceUseRisk', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select risk level" />
                </SelectTrigger>
                <SelectContent>
                  {RISK_LEVELS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Risk Factors and Protective Factors */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Risk & Protective Factors</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Risk Factors</Label>
            <Textarea
              value={formData.riskFactors || ''}
              onChange={(e) => handleStringChange('riskFactors', e.target.value)}
              placeholder="Describe specific risk factors (e.g., social isolation, recent losses, substance use, mental illness, access to means, etc.)"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label>Protective Factors</Label>
            <Textarea
              value={formData.protectiveFactors || ''}
              onChange={(e) => handleStringChange('protectiveFactors', e.target.value)}
              placeholder="Describe protective factors (e.g., social support, coping skills, treatment engagement, religious beliefs, etc.)"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label>Recommended Interventions</Label>
            <Textarea
              value={formData.interventions || ''}
              onChange={(e) => handleStringChange('interventions', e.target.value)}
              placeholder="Describe recommended interventions and safety planning measures"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
