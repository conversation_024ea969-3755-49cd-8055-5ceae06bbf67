import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

const symptoms = [
  { name: "Depressed mood", category: "Mood", description: "Persistent feelings of sadness or emptiness" },
  { name: "Anxiety", category: "Anxiety", description: "Excessive worry or fear" },
  { name: "Sleep disturbances", category: "Sleep", description: "Difficulty falling asleep, staying asleep, or early awakening" },
  { name: "Appetite changes", category: "Physical", description: "Significant increase or decrease in appetite" },
  { name: "Fatigue", category: "Physical", description: "Persistent tiredness or lack of energy" },
  { name: "Concentration difficulties", category: "Cognitive", description: "Trouble focusing or making decisions" },
  { name: "Irritability", category: "Mood", description: "Increased anger or frustration" },
  { name: "Mood swings", category: "Mood", description: "Rapid changes in emotional state" },
  { name: "Panic attacks", category: "Anxiety", description: "Sudden episodes of intense fear or discomfort" },
  { name: "Obsessive thoughts", category: "Thought", description: "Persistent, unwanted thoughts" },
  { name: "Compulsive behaviors", category: "Behavior", description: "Repetitive behaviors or mental acts" },
  { name: "Hallucinations", category: "Perceptual", description: "Seeing, hearing, or sensing things that aren't there" },
  { name: "Delusions", category: "Thought", description: "Fixed false beliefs" },
  { name: "Paranoia", category: "Thought", description: "Excessive suspicion or mistrust" },
  { name: "Social withdrawal", category: "Social", description: "Avoiding social interactions" },
  { name: "Suicidal thoughts", category: "Risk", description: "Thoughts of death or self-harm" },
  { name: "Self-harm behaviors", category: "Risk", description: "Intentional self-injury" },
  { name: "Substance use", category: "Substance", description: "Use of alcohol or drugs" },
  { name: "Memory problems", category: "Cognitive", description: "Difficulty remembering information" },
  { name: "Disorientation", category: "Cognitive", description: "Confusion about time, place, or person" },
  { name: "Hyperactivity", category: "Behavior", description: "Excessive physical activity" },
  { name: "Impulsivity", category: "Behavior", description: "Acting without thinking" },
  { name: "Emotional numbness", category: "Mood", description: "Inability to feel emotions" },
  { name: "Racing thoughts", category: "Thought", description: "Rapid succession of thoughts" },
  { name: "Grandiosity", category: "Thought", description: "Inflated self-esteem or sense of importance" }
]

const diagnoses = [
  { code: "F32.9", name: "Major Depressive Disorder, Single Episode, Unspecified", category: "Mood Disorders", description: "A major depressive episode without history of manic episodes" },
  { code: "F33.9", name: "Major Depressive Disorder, Recurrent, Unspecified", category: "Mood Disorders", description: "Recurrent major depressive episodes" },
  { code: "F41.1", name: "Generalized Anxiety Disorder", category: "Anxiety Disorders", description: "Excessive anxiety and worry about various events" },
  { code: "F41.0", name: "Panic Disorder", category: "Anxiety Disorders", description: "Recurrent unexpected panic attacks" },
  { code: "F43.10", name: "Post-Traumatic Stress Disorder, Unspecified", category: "Trauma-Related Disorders", description: "PTSD following exposure to traumatic event" },
  { code: "F31.9", name: "Bipolar Disorder, Unspecified", category: "Mood Disorders", description: "Bipolar disorder not meeting criteria for specific type" },
  { code: "F20.9", name: "Schizophrenia, Unspecified", category: "Psychotic Disorders", description: "Schizophrenia not specified as to type" },
  { code: "F42.2", name: "Mixed Obsessional Thoughts and Acts", category: "Obsessive-Compulsive Disorders", description: "Both obsessions and compulsions present" },
  { code: "F10.20", name: "Alcohol Use Disorder, Moderate", category: "Substance-Related Disorders", description: "Moderate alcohol use disorder" },
  { code: "F43.25", name: "Adjustment Disorder with Mixed Anxiety and Depressed Mood", category: "Stress-Related Disorders", description: "Adjustment disorder with both anxiety and depression" },
  { code: "F60.3", name: "Borderline Personality Disorder", category: "Personality Disorders", description: "Pervasive pattern of instability in relationships and self-image" },
  { code: "F90.9", name: "Attention-Deficit/Hyperactivity Disorder, Unspecified", category: "Neurodevelopmental Disorders", description: "ADHD not specified as to type" },
  { code: "F40.10", name: "Social Anxiety Disorder", category: "Anxiety Disorders", description: "Marked fear of social situations" },
  { code: "F34.1", name: "Dysthymic Disorder", category: "Mood Disorders", description: "Persistent depressive disorder" },
  { code: "F25.9", name: "Schizoaffective Disorder, Unspecified", category: "Psychotic Disorders", description: "Features of both schizophrenia and mood disorder" }
]

async function main() {
  console.log('Start seeding...')

  // Clear existing data
  await prisma.symptom.deleteMany()
  await prisma.diagnosis.deleteMany()

  // Seed symptoms
  for (const symptom of symptoms) {
    await prisma.symptom.create({
      data: symptom
    })
  }

  // Seed diagnoses
  for (const diagnosis of diagnoses) {
    await prisma.diagnosis.create({
      data: diagnosis
    })
  }

  console.log('Seeding finished.')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
